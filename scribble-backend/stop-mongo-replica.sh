#!/bin/bash

# MongoDB Replica Set Stop Script
# This script stops all MongoDB replica set instances

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}Stopping MongoDB Replica Set...${NC}"

# Function to stop MongoDB on a specific port
stop_mongo() {
    local port=$1
    local node_name=$2
    
    echo -e "${YELLOW}Stopping ${node_name} on port ${port}...${NC}"
    
    # Try graceful shutdown first
    mongosh --port ${port} --eval "db.adminCommand('shutdown')" --quiet 2>/dev/null || true
    
    # Wait a moment
    sleep 2
    
    # Check if still running and force kill if necessary
    local pid=$(lsof -ti:${port} 2>/dev/null || true)
    if [ ! -z "$pid" ]; then
        echo -e "${YELLOW}Force killing process on port ${port}...${NC}"
        kill -9 $pid 2>/dev/null || true
    fi
    
    # Verify it's stopped
    if ! lsof -Pi :${port} -sTCP:LISTEN -t >/dev/null 2>&1; then
        echo -e "${GREEN}${node_name} stopped successfully${NC}"
    else
        echo -e "${RED}Failed to stop ${node_name} on port ${port}${NC}"
    fi
}

# Stop all MongoDB instances
stop_mongo 27017 "mongo1"
stop_mongo 27018 "mongo2"
stop_mongo 27019 "mongo3"

echo -e "${GREEN}"
echo "=========================================="
echo "MongoDB Replica Set stopped!"
echo "=========================================="
echo -e "${NC}"
echo "Data is preserved in ./mongodb-data/"
echo "To start again, run: ./start-mongo-replica.sh"
