#!/bin/bash

# MongoDB Replica Set Startup Script
# This script starts a 3-node MongoDB replica set using local mongod

set -e

# Configuration
REPLICA_SET_NAME="rs0"
BASE_PORT=27017
DATA_DIR="./mongodb-data"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}Starting MongoDB Replica Set with 3 nodes...${NC}"

# Create data directories
echo -e "${YELLOW}Creating data directories...${NC}"
mkdir -p "${DATA_DIR}/mongo1"
mkdir -p "${DATA_DIR}/mongo2"
mkdir -p "${DATA_DIR}/mongo3"
mkdir -p "${DATA_DIR}/logs"

# Function to check if port is available
check_port() {
    local port=$1
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        echo -e "${RED}Port $port is already in use. Please stop any existing MongoDB instances.${NC}"
        exit 1
    fi
}

# Check if ports are available
echo -e "${YELLOW}Checking if ports are available...${NC}"
check_port 27017
check_port 27018
check_port 27019

# Function to start a MongoDB instance
start_mongo() {
    local node_name=$1
    local port=$2
    local data_path="${DATA_DIR}/${node_name}"
    local log_path="${DATA_DIR}/logs/${node_name}.log"
    
    echo -e "${YELLOW}Starting ${node_name} on port ${port}...${NC}"
    
    mongod \
        --replSet "${REPLICA_SET_NAME}" \
        --port ${port} \
        --dbpath "${data_path}" \
        --logpath "${log_path}" \
        --logappend \
        --bind_ip localhost \
        --fork \
        --quiet
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}${node_name} started successfully on port ${port}${NC}"
    else
        echo -e "${RED}Failed to start ${node_name}${NC}"
        exit 1
    fi
}

# Start MongoDB instances
start_mongo "mongo1" 27017
start_mongo "mongo2" 27018
start_mongo "mongo3" 27019

# Wait for instances to be ready
echo -e "${YELLOW}Waiting for MongoDB instances to be ready...${NC}"
sleep 5

# Initialize replica set
echo -e "${YELLOW}Initializing replica set...${NC}"
mongosh --port 27017 --eval "
rs.initiate({
  _id: '${REPLICA_SET_NAME}',
  members: [
    { _id: 0, host: 'localhost:27017', priority: 2 },
    { _id: 1, host: 'localhost:27018', priority: 1 },
    { _id: 2, host: 'localhost:27019', priority: 1 }
  ]
})
"

if [ $? -eq 0 ]; then
    echo -e "${GREEN}Replica set initialized successfully!${NC}"
else
    echo -e "${RED}Failed to initialize replica set${NC}"
    exit 1
fi

# Wait for replica set to stabilize
echo -e "${YELLOW}Waiting for replica set to stabilize...${NC}"
sleep 10

# Check replica set status
echo -e "${YELLOW}Checking replica set status...${NC}"
mongosh --port 27017 --eval "rs.status()" --quiet

echo -e "${GREEN}"
echo "=========================================="
echo "MongoDB Replica Set is now running!"
echo "=========================================="
echo -e "${NC}"
echo "Connection details:"
echo "  Primary:     mongodb://localhost:27017"
echo "  Secondary 1: mongodb://localhost:27018"
echo "  Secondary 2: mongodb://localhost:27019"
echo ""
echo "Replica Set Connection String:"
echo "  mongodb://localhost:27017,localhost:27018,localhost:27019/?replicaSet=${REPLICA_SET_NAME}"
echo ""
echo "For MongoDB Compass:"
echo "  mongodb://localhost:27017,localhost:27018,localhost:27019/?replicaSet=${REPLICA_SET_NAME}"
echo ""
echo "To stop the replica set, run:"
echo "  ./stop-mongo-replica.sh"
echo ""
echo "Data directories:"
echo "  ${DATA_DIR}/mongo1"
echo "  ${DATA_DIR}/mongo2"
echo "  ${DATA_DIR}/mongo3"
echo ""
echo "Log files:"
echo "  ${DATA_DIR}/logs/mongo1.log"
echo "  ${DATA_DIR}/logs/mongo2.log"
echo "  ${DATA_DIR}/logs/mongo3.log"
